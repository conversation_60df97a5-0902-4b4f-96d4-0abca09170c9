# OpenWebUI SSO Deployment Guide

Complete step-by-step guide for deploying OpenWebUI with Keycloak SSO authentication on AWS EC2 RHEL 9.

## Prerequisites

### System Requirements
- AWS EC2 instance (t3.large or larger recommended)
- RHEL 9 operating system
- At least 8GB RAM, 50GB storage
- Domain names configured with DNS
- Docker and Docker Compose installed

### Domain Configuration
You'll need these subdomains:
- `openwebui.your-domain.com` - Main OpenWebUI application
- `keycloak.your-domain.com` - Keycloak identity provider
- `proxy.your-domain.com` - Nginx Proxy Manager (optional)

## Step 1: System Preparation

### Install Docker and Docker Compose

```bash
# Update system
sudo dnf update -y

# Install Docker
sudo dnf install -y docker docker-compose

# Start and enable Docker
sudo systemctl start docker
sudo systemctl enable docker

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker

# Verify installation
docker --version
docker-compose --version
```

### Configure Firewall

```bash
# Configure firewall for web services
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=81/tcp
sudo firewall-cmd --reload
```

## Step 2: Environment Setup

### Create Project Directory

```bash
mkdir -p ~/openwebui-sso
cd ~/openwebui-sso

# Download configuration files
curl -O https://raw.githubusercontent.com/your-repo/realm-config.json
curl -O https://raw.githubusercontent.com/your-repo/openwebui-docker-compose.yml
```

### Configure Environment Variables

Create `.env` file:

```bash
cat > .env << 'EOF'
# Domain Configuration
DOMAIN=your-domain.com
OPENWEBUI_DOMAIN=openwebui.your-domain.com
KEYCLOAK_DOMAIN=keycloak.your-domain.com

# Database Passwords
POSTGRES_PASSWORD=your-secure-postgres-password
KEYCLOAK_DB_PASSWORD=your-secure-keycloak-db-password

# Keycloak Admin
KEYCLOAK_ADMIN_PASSWORD=your-secure-keycloak-admin-password

# OpenWebUI Configuration
OPENWEBUI_SECRET_KEY=your-random-secret-key-32-chars-min
OAUTH_CLIENT_SECRET=openwebui-client-secret-change-this

# SMTP Configuration (optional)
SMTP_HOST=smtp.your-domain.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-smtp-password
EOF
```

### Update Configuration Files

```bash
# Replace placeholder values in realm-config.json
sed -i 's/your-domain\.com/'$DOMAIN'/g' realm-config.json
sed -i 's/openwebui-client-secret-change-this-to-secure-value/'$OAUTH_CLIENT_SECRET'/g' realm-config.json

# Replace placeholder values in docker-compose.yml
sed -i 's/your-domain\.com/'$DOMAIN'/g' openwebui-docker-compose.yml
sed -i 's/your-postgres-password-change-this/'$POSTGRES_PASSWORD'/g' openwebui-docker-compose.yml
sed -i 's/your-keycloak-admin-password-change-this/'$KEYCLOAK_ADMIN_PASSWORD'/g' openwebui-docker-compose.yml
```

## Step 3: Deploy Services

### Start the Stack

```bash
# Start all services
docker-compose -f openwebui-docker-compose.yml up -d

# Monitor startup
docker-compose -f openwebui-docker-compose.yml logs -f
```

### Verify Service Health

```bash
# Check service status
docker-compose -f openwebui-docker-compose.yml ps

# Test internal connectivity
docker exec openwebui-app curl -f http://postgres:5432 || echo "DB not ready"
docker exec openwebui-app curl -f http://ollama:11434 || echo "Ollama not ready"
docker exec openwebui-app curl -f http://openwebui-keycloak:8080/health || echo "Keycloak not ready"
```

## Step 4: Configure Nginx Proxy Manager

### Initial Setup

1. Access Nginx Proxy Manager: `http://your-server-ip:81`
2. Login with default credentials:
   - Email: `<EMAIL>`
   - Password: `changeme`
3. Change default credentials immediately

### SSL Certificates

1. Go to **SSL Certificates** → **Add SSL Certificate**
2. Select **Let's Encrypt**
3. Add certificates for:
   - `openwebui.your-domain.com`
   - `keycloak.your-domain.com`

### Proxy Hosts

Follow the detailed configuration in `nginx-proxy-config-guide.md`

## Step 5: Configure Keycloak

### Access Keycloak Admin Console

1. Navigate to `https://keycloak.your-domain.com/admin/`
2. Login with admin credentials from `.env` file

### Import Realm Configuration

```bash
# Copy realm config to Keycloak container
docker cp realm-config.json openwebui-keycloak:/tmp/

# Import realm using admin CLI
docker exec openwebui-keycloak /opt/keycloak/bin/kcadm.sh config credentials \
  --server http://localhost:8080 \
  --realm master \
  --user admin \
  --password $KEYCLOAK_ADMIN_PASSWORD

docker exec openwebui-keycloak /opt/keycloak/bin/kcadm.sh create realms \
  -f /tmp/realm-config.json
```

### Verify Realm Configuration

1. Check realm exists: `https://keycloak.your-domain.com/realms/openwebui-realm`
2. Verify OIDC discovery: `https://keycloak.your-domain.com/realms/openwebui-realm/.well-known/openid-configuration`

## Step 6: Configure OpenWebUI

### Update OpenWebUI Environment

```bash
# Update OpenWebUI container with final configuration
docker-compose -f openwebui-docker-compose.yml restart openwebui
```

### Test Authentication Flow

1. Navigate to `https://openwebui.your-domain.com`
2. Click "Sign in with Keycloak SSO"
3. Complete OAuth flow
4. Verify user creation in OpenWebUI

## Step 7: User Management

### Create Initial Users

1. In Keycloak Admin Console, go to **Users** → **Add user**
2. Set required fields:
   - Username
   - Email
   - First Name, Last Name
3. Set password in **Credentials** tab
4. Assign roles in **Role Mappings** tab

### Configure User Roles

Available roles in OpenWebUI realm:
- `openwebui-user` - Basic chat access
- `openwebui-admin` - Full platform administration
- `openwebui-moderator` - User management
- `model-manager` - AI model management
- `pipeline-manager` - Data pipeline management

### Role Assignment

```bash
# Assign admin role to user
docker exec openwebui-keycloak /opt/keycloak/bin/kcadm.sh add-roles \
  -r openwebui-realm \
  --uusername admin-user \
  --rolename openwebui-admin
```

## Step 8: Testing and Validation

### Functional Testing

1. **Authentication Test**:
   - Access OpenWebUI
   - Login with Keycloak
   - Verify user profile

2. **Authorization Test**:
   - Test different user roles
   - Verify access controls

3. **Integration Test**:
   - Test chat functionality
   - Verify model access
   - Test file uploads

### Performance Testing

```bash
# Test response times
curl -w "@curl-format.txt" -o /dev/null -s https://openwebui.your-domain.com

# Monitor resource usage
docker stats
```

### Security Testing

```bash
# SSL/TLS testing
nmap --script ssl-enum-ciphers -p 443 openwebui.your-domain.com

# Security headers check
curl -I https://openwebui.your-domain.com
```

## Step 9: Monitoring and Maintenance

### Log Monitoring

```bash
# Monitor all services
docker-compose -f openwebui-docker-compose.yml logs -f

# Monitor specific service
docker logs -f openwebui-app
docker logs -f openwebui-keycloak
```

### Health Checks

```bash
# Create health check script
cat > health-check.sh << 'EOF'
#!/bin/bash
echo "Checking OpenWebUI services..."

# Check OpenWebUI
curl -f https://openwebui.your-domain.com/health || echo "OpenWebUI DOWN"

# Check Keycloak
curl -f https://keycloak.your-domain.com/health || echo "Keycloak DOWN"

# Check database
docker exec postgres pg_isready -U openwebui || echo "Database DOWN"

echo "Health check complete"
EOF

chmod +x health-check.sh
```

### Backup Strategy

```bash
# Backup script
cat > backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Backup PostgreSQL
docker exec postgres pg_dump -U openwebui openwebui > $BACKUP_DIR/openwebui.sql

# Backup Keycloak data
docker run --rm -v keycloak_data:/data -v $BACKUP_DIR:/backup alpine tar czf /backup/keycloak-data.tar.gz /data

# Backup OpenWebUI data
docker run --rm -v openwebui_data:/data -v $BACKUP_DIR:/backup alpine tar czf /backup/openwebui-data.tar.gz /data

echo "Backup completed: $BACKUP_DIR"
EOF

chmod +x backup.sh
```

## Step 10: Troubleshooting

### Common Issues

1. **OAuth Redirect Errors**:
   - Check redirect URIs in Keycloak client
   - Verify domain configuration

2. **SSL Certificate Issues**:
   - Check Let's Encrypt logs
   - Verify DNS configuration

3. **Database Connection Issues**:
   - Check PostgreSQL logs
   - Verify network connectivity

### Debug Commands

```bash
# Check service connectivity
docker exec openwebui-app curl http://postgres:5432
docker exec openwebui-app curl http://openwebui-keycloak:8080/health

# Check environment variables
docker exec openwebui-app env | grep OAUTH

# Check Keycloak realm
curl https://keycloak.your-domain.com/realms/openwebui-realm/.well-known/openid-configuration
```

### Support Resources

- OpenWebUI Documentation: https://docs.openwebui.com
- Keycloak Documentation: https://www.keycloak.org/documentation
- Docker Compose Reference: https://docs.docker.com/compose/

## Conclusion

Your OpenWebUI platform with Keycloak SSO is now deployed and configured. Users can access the platform through `https://openwebui.your-domain.com` and authenticate using the configured identity providers.

Remember to:
- Regularly update container images
- Monitor system resources
- Backup data regularly
- Review security configurations periodically
