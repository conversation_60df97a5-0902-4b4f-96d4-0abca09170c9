# =============================================================================
# SECURITY WARNING: Copy this file to .env and update all values!
# DO NOT use these example values in production!
# =============================================================================

# Domain Configuration
DOMAIN=your-domain.com
WEBUI_URL=https://openwebui.your-domain.com
KEYCLOAK_HOSTNAME=keycloak.your-domain.com

# Database Configuration
# CRITICAL: Change these passwords to secure random values!
POSTGRES_PASSWORD=your-secure-postgres-password-change-this
OPENWEBUI_DB_PASSWORD=your-secure-openwebui-db-password-change-this

# Keycloak Configuration
KEYCLOAK_ADMIN_USER=admin
KEYCLOAK_ADMIN_PASSWORD=your-secure-keycloak-admin-password-change-this

# OpenWebUI Configuration
WEBUI_NAME=OpenWebUI Multi-User Platform
WEBUI_SECRET_KEY=your-32-character-secret-key-change-this-to-random-string
WEBUI_LOG_LEVEL=INFO

# OAuth/OIDC Configuration
OAUTH_CLIENT_ID=openwebui
OAUTH_CLIENT_SECRET=your-secure-oauth-client-secret-change-this
OAUTH_PROVIDER_NAME=Keycloak SSO
OPENID_PROVIDER_URL=https://keycloak.your-domain.com/realms/openwebui-realm
OAUTH_SCOPES=openid profile email
OAUTH_ROLES_CLAIM=openwebui_roles
OAUTH_REDIRECT_URI=https://openwebui.your-domain.com/oauth/oidc/callback
OAUTH_MERGE_ACCOUNTS_BY_EMAIL=true

# User Management
DEFAULT_USER_ROLE=user
ENABLE_ADMIN_EXPORT=true
ENABLE_ADMIN_CHAT_ACCESS=true

# Security Settings
ENABLE_COMMUNITY_SHARING=false
ENABLE_MESSAGE_RATING=true
ENABLE_MODEL_FILTER=true

# AI and RAG Features
DEFAULT_MODELS=llama2:latest
ENABLE_IMAGE_GENERATION=true
ENABLE_RAG_WEB_SEARCH=true
ENABLE_RAG_LOCAL_WEB_FETCH=true
RAG_WEB_SEARCH_ENGINE=searxng
RAG_EMBEDDING_MODEL=nomic-embed-text:latest

# =============================================================================
# SETUP INSTRUCTIONS:
# 1. Copy this file: cp .env.example .env
# 2. Generate secure passwords and secrets
# 3. Update all domain names to match your setup
# 4. Review and customize feature flags as needed
# =============================================================================
