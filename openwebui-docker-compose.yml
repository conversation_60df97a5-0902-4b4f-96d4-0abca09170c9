version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: openwebui-postgres
    environment:
      POSTGRES_DB: openwebui
      POSTGRES_USER: openwebui
      POSTGRES_PASSWORD: your-postgres-password-change-this
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - openwebui-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U openwebui"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Keycloak Identity Provider
  keycloak:
    image: quay.io/keycloak/keycloak:23.0
    container_name: openwebui-keycloak
    environment:
      KC_DB: postgres
      KC_DB_URL: ****************************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: your-keycloak-db-password-change-this
      KC_HOSTNAME: keycloak.your-domain.com
      KC_HOSTNAME_STRICT: false
      KC_HOSTNAME_STRICT_HTTPS: false
      KC_HTTP_ENABLED: true
      KC_PROXY: edge
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: your-keycloak-admin-password-change-this
    command: start
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - openwebui-network
    ports:
      - "8080:8080"
    restart: unless-stopped
    volumes:
      - keycloak_data:/opt/keycloak/data

  # Ollama LLM Backend
  ollama:
    image: ollama/ollama:latest
    container_name: openwebui-ollama
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - openwebui-network
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  # Apache Tika Document Processing
  tika:
    image: apache/tika:latest
    container_name: openwebui-tika
    networks:
      - openwebui-network
    restart: unless-stopped

  # OpenWebUI Pipeline
  openwebui-pipeline:
    image: ghcr.io/open-webui/pipelines:main
    container_name: openwebui-pipeline
    environment:
      - PIPELINES_URLS=https://github.com/open-webui/pipelines
    volumes:
      - pipeline_data:/app/pipelines
    networks:
      - openwebui-network
    restart: unless-stopped

  # OpenWebUI Main Application
  openwebui:
    image: ghcr.io/open-webui/open-webui:main
    container_name: openwebui-app
    environment:
      # Database Configuration
      - DATABASE_URL=***********************************************************************/openwebui
      
      # Ollama Configuration
      - OLLAMA_BASE_URL=http://ollama:11434
      
      # Tika Configuration
      - TIKA_SERVER_URL=http://tika:9998
      
      # Pipeline Configuration
      - OPENAI_API_BASE_URL=http://openwebui-pipeline:9099
      
      # OpenWebUI Configuration
      - WEBUI_NAME=OpenWebUI Multi-User Platform
      - WEBUI_URL=https://openwebui.your-domain.com
      - WEBUI_SECRET_KEY=your-secret-key-change-this-to-random-string
      
      # Authentication Configuration
      - ENABLE_OAUTH_SIGNUP=true
      - OAUTH_MERGE_ACCOUNTS_BY_EMAIL=true
      
      # OIDC/Keycloak Configuration
      - OAUTH_CLIENT_ID=openwebui
      - OAUTH_CLIENT_SECRET=openwebui-client-secret-change-this-to-secure-value
      - OAUTH_PROVIDER_NAME=Keycloak SSO
      - OPENID_PROVIDER_URL=https://keycloak.your-domain.com/realms/openwebui-realm
      - OAUTH_SCOPES=openid profile email openwebui-scope
      - OAUTH_USERNAME_CLAIM=preferred_username
      - OAUTH_EMAIL_CLAIM=email
      - OAUTH_PICTURE_CLAIM=picture
      - OAUTH_ROLES_CLAIM=openwebui_roles
      
      # User Management
      - DEFAULT_USER_ROLE=openwebui-user
      - ENABLE_ADMIN_EXPORT=true
      - ENABLE_ADMIN_CHAT_ACCESS=true
      
      # Security Settings
      - ENABLE_COMMUNITY_SHARING=false
      - ENABLE_MESSAGE_RATING=true
      - ENABLE_MODEL_FILTER=true
      
      # File Upload Settings
      - ENABLE_IMAGE_GENERATION=true
      - ENABLE_RAG_WEB_SEARCH=true
      - RAG_WEB_SEARCH_ENGINE=searxng
      
      # Logging
      - WEBUI_LOG_LEVEL=INFO
      
    volumes:
      - openwebui_data:/app/backend/data
    depends_on:
      - postgres
      - ollama
      - tika
      - openwebui-pipeline
    networks:
      - openwebui-network
    restart: unless-stopped

  # Nginx Proxy Manager
  nginx-proxy-manager:
    image: jc21/nginx-proxy-manager:latest
    container_name: nginx-proxy-manager
    environment:
      - DISABLE_IPV6=true
    ports:
      - "80:80"
      - "443:443"
      - "81:81"
    volumes:
      - nginx_data:/data
      - nginx_letsencrypt:/etc/letsencrypt
    networks:
      - openwebui-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  keycloak_data:
    driver: local
  ollama_data:
    driver: local
  pipeline_data:
    driver: local
  openwebui_data:
    driver: local
  nginx_data:
    driver: local
  nginx_letsencrypt:
    driver: local

networks:
  openwebui-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
