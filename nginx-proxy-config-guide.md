# Nginx Proxy Manager Configuration for OpenWebUI SSO

This guide provides step-by-step instructions for configuring Nginx Proxy Manager to work with OpenWebUI and Keycloak SSO in a containerized environment.

## Prerequisites

- Docker and Docker Compose installed
- Domain names configured (DNS pointing to your server)
- SSL certificates (Let's Encrypt recommended)

## 1. Nginx Proxy Manager Setup

### Access the Admin Interface

1. Navigate to `http://your-server-ip:81`
2. Default login credentials:
   - Email: `<EMAIL>`
   - Password: `changeme`
3. Change the default credentials immediately

## 2. SSL Certificate Configuration

### Let's Encrypt SSL Certificates

1. Go to **SSL Certificates** → **Add SSL Certificate**
2. Select **Let's Encrypt**
3. Configure certificates for:
   - `openwebui.your-domain.com`
   - `keycloak.your-domain.com`
   - `proxy.your-domain.com` (optional)

### Certificate Settings
```
Domain Names: openwebui.your-domain.com
Email: <EMAIL>
Use a DNS Challenge: No (use HTTP-01 challenge)
Agree to Terms: Yes
```

## 3. Proxy Host Configuration

### OpenWebUI Proxy Host

1. Go to **Hosts** → **Proxy Hosts** → **Add Proxy Host**

#### Details Tab
```
Domain Names: openwebui.your-domain.com
Scheme: http
Forward Hostname/IP: openwebui-app
Forward Port: 8080
Cache Assets: Yes
Block Common Exploits: Yes
Websockets Support: Yes
```

#### SSL Tab
```
SSL Certificate: Select your Let's Encrypt certificate
Force SSL: Yes
HTTP/2 Support: Yes
HSTS Enabled: Yes
HSTS Subdomains: Yes
```

#### Advanced Tab
```nginx
# OpenWebUI specific configuration
location / {
    proxy_pass http://openwebui-app:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
    
    # WebSocket support for real-time features
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    
    # Increase timeouts for long-running requests
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # Handle large file uploads
    client_max_body_size 100M;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
}

# OAuth callback handling
location /oauth/ {
    proxy_pass http://openwebui-app:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
}

# API endpoints
location /api/ {
    proxy_pass http://openwebui-app:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # Increase timeout for API calls
    proxy_read_timeout 300s;
    proxy_send_timeout 300s;
}
```

### Keycloak Proxy Host

1. Add another **Proxy Host** for Keycloak

#### Details Tab
```
Domain Names: keycloak.your-domain.com
Scheme: http
Forward Hostname/IP: openwebui-keycloak
Forward Port: 8080
Cache Assets: No
Block Common Exploits: Yes
Websockets Support: No
```

#### SSL Tab
```
SSL Certificate: Select your Let's Encrypt certificate
Force SSL: Yes
HTTP/2 Support: Yes
HSTS Enabled: Yes
```

#### Advanced Tab
```nginx
# Keycloak specific configuration
location / {
    proxy_pass http://openwebui-keycloak:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
    
    # Keycloak requires these headers for proper operation
    proxy_set_header X-Forwarded-Server $host;
    proxy_set_header X-Forwarded-Ssl on;
    
    # Buffer settings for Keycloak admin console
    proxy_buffer_size 128k;
    proxy_buffers 4 256k;
    proxy_busy_buffers_size 256k;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}

# Keycloak admin console
location /admin/ {
    proxy_pass http://openwebui-keycloak:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
    
    # Increase timeout for admin operations
    proxy_read_timeout 300s;
    proxy_send_timeout 300s;
}

# Keycloak realm endpoints
location /realms/ {
    proxy_pass http://openwebui-keycloak:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
}
```

## 4. Docker Network Configuration

### Ensure Proper Network Connectivity

1. All services must be on the same Docker network
2. Use service names for internal communication
3. Example network configuration in docker-compose.yml:

```yaml
networks:
  openwebui-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

## 5. Security Considerations

### Firewall Configuration

```bash
# Allow only necessary ports
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 81/tcp  # Nginx Proxy Manager admin (restrict to admin IPs)
sudo ufw deny 8080/tcp  # Block direct access to services
```

### Additional Security Headers

Add these to your Nginx configuration:

```nginx
# Content Security Policy
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss: https:;" always;

# Additional security headers
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
add_header X-Permitted-Cross-Domain-Policies "none" always;
add_header X-Robots-Tag "none" always;
```

## 6. Testing the Configuration

### Verify SSL Certificates

```bash
# Test SSL certificate
curl -I https://openwebui.your-domain.com
curl -I https://keycloak.your-domain.com

# Check certificate details
openssl s_client -connect openwebui.your-domain.com:443 -servername openwebui.your-domain.com
```

### Test Proxy Functionality

1. Access `https://openwebui.your-domain.com`
2. Verify redirect to Keycloak for authentication
3. Test OAuth callback flow
4. Verify WebSocket connections work

### Monitor Logs

```bash
# Nginx Proxy Manager logs
docker logs nginx-proxy-manager

# OpenWebUI logs
docker logs openwebui-app

# Keycloak logs
docker logs openwebui-keycloak
```

## 7. Troubleshooting

### Common Issues

1. **502 Bad Gateway**: Check service connectivity and Docker network
2. **SSL Certificate Issues**: Verify DNS and Let's Encrypt configuration
3. **OAuth Redirect Issues**: Check redirect URIs in Keycloak client configuration
4. **WebSocket Connection Failures**: Ensure WebSocket support is enabled

### Debug Commands

```bash
# Test internal connectivity
docker exec nginx-proxy-manager curl http://openwebui-app:8080/health
docker exec nginx-proxy-manager curl http://openwebui-keycloak:8080/health

# Check DNS resolution
docker exec nginx-proxy-manager nslookup openwebui-app
docker exec nginx-proxy-manager nslookup openwebui-keycloak
```

## 8. Maintenance

### Certificate Renewal

Let's Encrypt certificates auto-renew, but monitor:

```bash
# Check certificate expiry
docker exec nginx-proxy-manager ls -la /etc/letsencrypt/live/
```

### Backup Configuration

```bash
# Backup Nginx Proxy Manager data
docker run --rm -v nginx_data:/data -v $(pwd):/backup alpine tar czf /backup/nginx-backup.tar.gz /data
```

This configuration ensures secure, scalable access to your OpenWebUI platform with proper SSL termination and SSO integration.
