#!/bin/bash

# OpenWebUI with Keycloak SSO Deployment Script
# For EC2 RHEL 9 with Docker

set -e

echo "🚀 OpenWebUI with Keycloak SSO Deployment Script"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f .env ]; then
    print_warning ".env file not found. Creating from template..."
    if [ -f .env.example ]; then
        cp .env.example .env
        print_warning "Please edit .env file with your actual values before continuing!"
        print_warning "Required changes:"
        echo "  - Update all domain names"
        echo "  - Generate secure passwords"
        echo "  - Configure OAuth settings"
        echo ""
        read -p "Press Enter after updating .env file..."
    else
        print_error ".env.example file not found!"
        exit 1
    fi
fi

# Check if postgres directory exists
if [ ! -d "postgres" ]; then
    print_status "Creating postgres directory..."
    mkdir -p postgres
fi

# Make database init script executable
if [ -f "postgres/init-multiple-databases.sh" ]; then
    chmod +x postgres/init-multiple-databases.sh
    print_status "Database initialization script made executable"
fi

# Secure .env file permissions
chmod 600 .env
print_status "Secured .env file permissions"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    print_error "Docker Compose is not installed."
    exit 1
fi

# Validate environment variables
print_status "Validating environment configuration..."
source .env

required_vars=(
    "DOMAIN"
    "POSTGRES_PASSWORD"
    "OPENWEBUI_DB_PASSWORD"
    "KEYCLOAK_ADMIN_PASSWORD"
    "WEBUI_SECRET_KEY"
    "OAUTH_CLIENT_SECRET"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        print_error "Required environment variable $var is not set in .env file"
        exit 1
    fi
done

# Check for default/example values that should be changed
if [[ "$POSTGRES_PASSWORD" == *"change-this"* ]] || [[ "$POSTGRES_PASSWORD" == *"your-"* ]]; then
    print_error "Please update POSTGRES_PASSWORD in .env file with a secure password"
    exit 1
fi

print_status "Environment validation passed"

# Pull latest images
print_status "Pulling latest Docker images..."
docker-compose pull

# Start services
print_status "Starting services..."
docker-compose up -d

# Wait for services to be healthy
print_status "Waiting for services to become healthy..."
sleep 30

# Check service status
print_status "Checking service status..."
docker-compose ps

# Display access information
echo ""
echo "🎉 Deployment completed!"
echo "======================="
echo ""
echo "Service URLs:"
echo "  - OpenWebUI: https://${WEBUI_URL:-openwebui.your-domain.com}"
echo "  - Keycloak Admin: https://${KEYCLOAK_HOSTNAME:-keycloak.your-domain.com}:9090"
echo "  - Nginx Proxy Manager: http://your-server-ip:81"
echo ""
echo "Default Credentials:"
echo "  - Keycloak Admin: ${KEYCLOAK_ADMIN_USER:-admin} / [your-password]"
echo "  - Nginx Proxy Manager: <EMAIL> / changeme"
echo ""
echo "Next Steps:"
echo "  1. Configure SSL certificates in Nginx Proxy Manager"
echo "  2. Set up Keycloak realm and client configuration"
echo "  3. Test OAuth authentication flow"
echo "  4. Configure firewall rules for ports 80, 443, 81, 9090"
echo ""
print_warning "Remember to change default passwords and secure your deployment!"
