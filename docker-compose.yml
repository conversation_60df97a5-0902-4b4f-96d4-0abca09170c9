version: "3.9"

# Docker Compose file for AI Services Platform with CUDA support
# SECURITY NOTICE:
# - Copy .env.example to .env and update all passwords and secrets
# - Ensure proper firewall configuration on your EC2 instance
# - Use strong passwords and enable 2FA where possible
# - Regularly update container images for security patches

services:
  # PostgreSQL Database for Nginx Proxy Manager
  db:
    image: postgres:latest
    networks:
      - nginx-proxy-net
    ports:
      - "5432:5432"
    restart: always
    shm_size: 128mb
    environment:
      POSTGRES_USER: 'npm'
      POSTGRES_PASSWORD: 'npmpass'
      POSTGRES_DB: 'npm'
    volumes:
      - ./postgres:/var/lib/postgresql/data

  # Keycloak Identity and Access Management
  keycloak:
    image: quay.io/keycloak/keycloak:latest
    networks:
      - nginx-proxy-net
    ports:
      - "9090:8080"
    environment:
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin
    command: start-dev
    restart: always

  # Nginx Proxy Manager
  app:
    image: 'jc21/nginx-proxy-manager:latest'
    networks:
      - nginx-proxy-net
    ports:
      - "80:80"  # Public HTTP Port
      - "443:443"  # Public HTTPS Port
      - "81:81"  # Admin Web Port
    environment:
      DB_POSTGRES_HOST: 'db'
      DB_POSTGRES_PORT: '5432'
      DB_POSTGRES_USER: 'npm'
      DB_POSTGRES_PASSWORD: 'npmpass'
      DB_POSTGRES_NAME: 'npm'
      DISABLE_IPV6: 'true'
    volumes:
      - ./data:/data
      - ./letsencrypt:/etc/letsencrypt
    restart: unless-stopped
    depends_on:
      - db

  # Ollama - Local LLM Inference with CUDA
  ollama:
    image: ollama/ollama
    runtime: nvidia
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]
    privileged: true
    networks:
      - nginx-proxy-net
    ports:
      - "11434:11434"
    restart: always
    environment:
      OLLAMA_ORIGINS: "*"
      OLLAMA_INSECURE: "true"
    volumes:
      - ollama:/root/.ollama
      - /etc/ssl/certs:/etc/ssl/certs:ro

  # OpenWebUI with CUDA Support
  open_webui:
    image: ghcr.io/open-webui/open-webui:cuda
    runtime: nvidia
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]
    privileged: true
    networks:
      - nginx-proxy-net
    ports:
      - "3000:8080"
    volumes:
      - open-webui:/app/backend/data
    restart: always
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # OpenWebUI Pipelines - Pipeline Processing Service
  pipelines:
    image: ghcr.io/open-webui/pipelines:main
    networks:
      - nginx-proxy-net
    ports:
      - "9099:9099"
    restart: always
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - pipelines:/app/pipelines

  # Apache Tika - Document Processing
  tika:
    image: apache/tika:latest-full
    networks:
      - nginx-proxy-net
    ports:
      - "9998:9998"
    restart: unless-stopped

networks:
  nginx-proxy-net:
    driver: bridge

volumes:
  open-webui:
  ollama:
  pipelines:
  postgres_data:
