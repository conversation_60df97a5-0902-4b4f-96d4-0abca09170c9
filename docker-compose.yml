# Docker Compose file for AI Services Platform with CUDA support
# Note: version attribute is obsolete in newer Docker Compose versions

services:
  # PostgreSQL Database for Keycloak
  postgres:
    image: postgres:15
    container_name: keycloak-postgres
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      # Ensure consistent encoding and authentication
      POSTGRES_INITDB_ARGS: "--auth-host=md5 --auth-local=trust"
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ai-services-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U keycloak -d keycloak"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 30s
    command: >
      postgres
      -c shared_preload_libraries=''
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100

  # Keycloak Identity and Access Management
  keycloak:
    image: quay.io/keycloak/keycloak:23.0
    container_name: keycloak
    environment:
      KC_DB: postgres
      KC_DB_URL: ****************************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: ${POSTGRES_PASSWORD}
      KC_HOSTNAME: ${KEYCLOAK_HOSTNAME}
      KC_HOSTNAME_STRICT: false
      KC_HOSTNAME_STRICT_HTTPS: false
      KC_HTTP_ENABLED: true
      KC_PROXY: edge
      KC_HEALTH_ENABLED: true
      KC_METRICS_ENABLED: true
      KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN_USER}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
      # JVM and startup optimizations
      JAVA_OPTS: "-Xms512m -Xmx2g -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true"
      KC_LOG_LEVEL: INFO
      KC_DB_POOL_INITIAL_SIZE: 5
      KC_DB_POOL_MIN_SIZE: 5
      KC_DB_POOL_MAX_SIZE: 20
      # Database connection settings
      KC_DB_URL_HOST: postgres
      KC_DB_URL_PORT: 5432
      KC_DB_URL_DATABASE: keycloak
      KC_DB_SCHEMA: public
      # Startup optimization
      KC_CACHE: local
      KC_CACHE_STACK: tcp
    volumes:
      # Mount custom realm configuration
      - ./keycloak/realm-config.json:/opt/keycloak/data/import/realm-config.json:ro
      # Persist Keycloak data
      - keycloak_data:/opt/keycloak/data
    command: start --import-realm
    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "9090:8080"
    networks:
      - ai-services-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health/ready || curl -f http://localhost:8080/health || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 8
      start_period: 90s

  # Nginx Proxy Manager
  nginx-proxy-manager:
    image: jc21/nginx-proxy-manager:latest
    container_name: nginx-proxy-manager
    ports:
      - "80:80"     # HTTP
      - "443:443"   # HTTPS
      - "81:81"     # Admin Web Port
    environment:
      DB_SQLITE_FILE: "/data/database.sqlite"
      DISABLE_IPV6: 'true'
    volumes:
      - npm_data:/data
      - npm_letsencrypt:/etc/letsencrypt
    networks:
      - ai-services-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:81"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Ollama - Local LLM Inference with CUDA
  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - ai-services-network
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
    healthcheck:
      test: ["CMD-SHELL", "ollama list || exit 1"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 60s

  # OpenWebUI with CUDA Support
  open-webui:
    image: ghcr.io/open-webui/open-webui:cuda
    container_name: open-webui
    environment:
      # Authentication Configuration
      WEBUI_AUTH: true
      ENABLE_OAUTH_SIGNUP: true
      OAUTH_PROVIDER_NAME: "Keycloak"
      OAUTH_CLIENT_ID: ${OAUTH_CLIENT_ID}
      OAUTH_CLIENT_SECRET: ${OAUTH_CLIENT_SECRET}
      OAUTH_SERVER_URL: ${OAUTH_SERVER_URL}
      OAUTH_SCOPE: "openid profile email"
      OAUTH_USERNAME_CLAIM: "preferred_username"
      OAUTH_EMAIL_CLAIM: "email"
      OAUTH_PICTURE_CLAIM: "picture"
      OAUTH_REDIRECT_URI: ${OAUTH_REDIRECT_URI}
      # Ollama Configuration
      OLLAMA_BASE_URL: "http://ollama:11434"
      # Pipelines Configuration
      OPENWEBUI_PIPELINES_URL: "http://pipelines:9099"
      # Apache Tika Configuration
      TIKA_BASE_URL: "http://tika:9998"
      # CUDA Configuration
      NVIDIA_VISIBLE_DEVICES: all
      NVIDIA_DRIVER_CAPABILITIES: compute,utility
      # Additional Configuration
      WEBUI_SECRET_KEY: ${WEBUI_SECRET_KEY}
      DEFAULT_MODELS: "llama2:latest"
      # Enable additional features
      ENABLE_RAG_WEB_SEARCH: true
      ENABLE_RAG_LOCAL_WEB_FETCH: true
      RAG_EMBEDDING_ENGINE: "ollama"
      RAG_EMBEDDING_MODEL: "nomic-embed-text:latest"
    volumes:
      - open_webui_data:/app/backend/data
    ports:
      - "3000:8080"
    networks:
      - ai-services-network
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    depends_on:
      keycloak:
        condition: service_healthy
      ollama:
        condition: service_healthy
      tika:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 45s

  # OpenWebUI Pipelines - Pipeline Processing Service
  pipelines:
    image: ghcr.io/open-webui/pipelines:main
    container_name: pipelines
    environment:
      # Ollama Configuration for Pipelines
      OLLAMA_BASE_URL: "http://ollama:11434"
      # OpenWebUI Integration
      OPENWEBUI_URL: "http://open-webui:8080"
      # Enable additional pipeline features
      ENABLE_OPENAI_API: true
      ENABLE_OLLAMA_API: true
    ports:
      - "9099:9099"
    volumes:
      - pipelines_data:/app/pipelines
    networks:
      - ai-services-network
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9099/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 45s
    depends_on:
      ollama:
        condition: service_healthy

  # Apache Tika - Document Processing
  tika:
    image: apache/tika:latest
    container_name: apache-tika
    ports:
      - "9998:9998"
    volumes:
      - tika_data:/tmp/tika-server
    networks:
      - ai-services-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9998/version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    environment:
      - TIKA_CONFIG_FILE=/tika-config.xml
      - JAVA_OPTS=-Xmx2g -Xms512m

volumes:
  postgres_data:
    driver: local
  keycloak_data:
    driver: local
  npm_data:
    driver: local
  npm_letsencrypt:
    driver: local
  open_webui_data:
    driver: local
  ollama_data:
    driver: local
  pipelines_data:
    driver: local
  tika_data:
    driver: local

networks:
  ai-services-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
