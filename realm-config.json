{"id": "openwebui-realm", "realm": "openwebui-realm", "displayName": "OpenWebUI SSO Realm", "displayNameHtml": "<div class=\"kc-logo-text\"><span>OpenWebUI Multi-User Platform</span></div>", "enabled": true, "// === BASIC REALM SETTINGS ===": "", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "// === SSL/SECURITY SETTINGS ===": "", "sslRequired": "external", "loginTheme": "keycloak", "accountTheme": "keycloak", "adminTheme": "keycloak", "emailTheme": "keycloak", "// === TOKEN LIFESPANS (in seconds) ===": "", "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "// === USER REGISTRATION & LOGIN SETTINGS ===": "", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": true, "verifyEmail": true, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": true, "editUsernameAllowed": false, "// === BRUTE FORCE PROTECTION ===": "", "bruteForceProtected": true, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "// === REQUIRED CREDENTIALS ===": "", "requiredCredentials": ["password"], "// === DEFAULT ROLES ===": "", "defaultRoles": ["default-roles-openwebui-realm"], "// === SMTP SERVER CONFIGURATION ===": "", "smtpServer": {"from": "<EMAIL>", "fromDisplayName": "OpenWebUI Platform", "replyTo": "<EMAIL>", "replyToDisplayName": "OpenWebUI Admin", "envelopeFrom": "<EMAIL>", "host": "smtp.your-domain.com", "port": "587", "ssl": "false", "starttls": "true", "auth": "true", "user": "<EMAIL>", "password": "your-smtp-password"}, "// === INTERNATIONALIZATION ===": "", "internationalizationEnabled": true, "supportedLocales": ["en", "es", "fr", "de"], "defaultLocale": "en", "// === REALM ROLES ===": "", "roles": {"realm": [{"name": "default-roles-openwebui-realm", "description": "Default realm role for OpenWebUI users", "composite": true, "composites": {"client": {"account": ["view-profile", "manage-account"]}}}, {"name": "openwebui-user", "description": "Standard OpenWebUI user with basic access to chat and models"}, {"name": "openwebui-admin", "description": "OpenWebUI administrator with full platform access"}, {"name": "openwebui-moderator", "description": "OpenWebUI moderator with user management capabilities"}, {"name": "model-manager", "description": "Can manage and configure AI models in OpenWebUI"}, {"name": "pipeline-manager", "description": "Can manage data processing pipelines"}]}, "// === CLIENT CONFIGURATIONS ===": "", "clients": [{"// === OPENWEBUI OIDC CLIENT ===": "", "clientId": "openwebui", "name": "OpenWebUI Application", "description": "OpenWebUI multi-user chat interface with SSO authentication", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "openwebui-client-secret-change-this-to-secure-value", "protocol": "openid-connect", "publicClient": false, "bearerOnly": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "authorizationServicesEnabled": false, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email", "openwebui-scope"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"], "rootUrl": "https://openwebui.your-domain.com", "baseUrl": "/", "adminUrl": "https://openwebui.your-domain.com/admin", "redirectUris": ["https://openwebui.your-domain.com/*", "https://openwebui.your-domain.com/oauth/callback", "https://openwebui.your-domain.com/auth/oidc/callback", "http://localhost:3000/*", "http://openwebui:3000/*"], "postLogoutRedirectUris": ["https://openwebui.your-domain.com/", "https://openwebui.your-domain.com/auth/logout"], "webOrigins": ["https://openwebui.your-domain.com", "http://localhost:3000", "http://openwebui:3000"], "notBefore": 0, "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "oidc.ciba.grant.enabled": "false", "saml.signature.algorithm": "RSA_SHA256", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false", "access.token.lifespan": "300", "client.session.idle.timeout": "1800", "client.session.max.lifespan": "36000", "pkce.code.challenge.method": "S256"}, "protocolMappers": [{"name": "openwebui-roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "roles", "jsonType.label": "String", "multivalued": "true"}}, {"name": "openwebui-groups", "protocol": "openid-connect", "protocolMapper": "oidc-group-membership-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "full.path": "false"}}, {"name": "openwebui-permissions", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "openwebui_permissions", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "openwebui_permissions", "jsonType.label": "String"}}]}, {"// === OPENWEBUI ADMIN CLIENT FOR USER MANAGEMENT ===": "", "clientId": "openwebui-admin", "name": "OpenWebUI Admin Client", "description": "Service account for OpenWebUI to manage users and roles", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "openwebui-admin-secret-change-this-to-secure-value", "protocol": "openid-connect", "publicClient": false, "bearerOnly": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "authorizationServicesEnabled": false, "fullScopeAllowed": false, "attributes": {"access.token.lifespan": "3600", "client.session.idle.timeout": "3600", "client.session.max.lifespan": "7200"}}], "// === IDENTITY PROVIDERS ===": "", "identityProviders": [{"// === GOOGLE OIDC PROVIDER ===": "", "alias": "google", "providerId": "google", "enabled": true, "updateProfileFirstLoginMode": "on", "trustEmail": true, "storeToken": false, "addReadTokenRoleOnCreate": false, "authenticateByDefault": false, "linkOnly": false, "firstBrokerLoginFlowAlias": "first broker login", "config": {"syncMode": "IMPORT", "clientId": "your-google-client-id.apps.googleusercontent.com", "clientSecret": "your-google-client-secret", "hostedDomain": "mycompany.com", "useJwksUrl": "true", "hideOnLoginPage": "false", "loginHint": "false", "uiLocales": "false", "backchannelSupported": "false", "disableUserInfo": "false", "acceptsPromptNoneForwardFromClient": "false", "filteredByClaim": "false"}}, {"// === SAML IDENTITY PROVIDER ===": "", "alias": "corporate-saml", "providerId": "saml", "enabled": true, "updateProfileFirstLoginMode": "on", "trustEmail": true, "storeToken": false, "addReadTokenRoleOnCreate": false, "authenticateByDefault": false, "linkOnly": false, "firstBrokerLoginFlowAlias": "first broker login", "config": {"syncMode": "IMPORT", "nameIDPolicyFormat": "urn:oasis:names:tc:SAML:2.0:nameid-format:persistent", "principalType": "SUBJECT", "signatureAlgorithm": "RSA_SHA256", "xmlSigKeyInfoKeyNameTransformer": "KEY_ID", "allowCreate": "true", "entityId": "https://keycloak.company.com/realms/my-sso-realm", "authnContextComparisonType": "exact", "hideOnLoginPage": "false", "backchannelSupported": "false", "postBindingResponse": "true", "postBindingAuthnRequest": "true", "postBindingLogout": "true", "wantAuthnRequestsSigned": "false", "wantAssertionsSigned": "true", "wantAssertionsEncrypted": "false", "forceAuthn": "false", "validateSignature": "true", "signSpMetadata": "false", "singleSignOnServiceUrl": "https://corporate-idp.company.com/sso/saml", "singleLogoutServiceUrl": "https://corporate-idp.company.com/slo/saml", "allowedClockSkew": "0", "attributeConsumingServiceIndex": "0"}}, {"// === MICROSOFT AZURE AD OIDC ===": "", "alias": "microsoft", "providerId": "microsoft", "enabled": true, "updateProfileFirstLoginMode": "on", "trustEmail": true, "storeToken": false, "addReadTokenRoleOnCreate": false, "authenticateByDefault": false, "linkOnly": false, "firstBrokerLoginFlowAlias": "first broker login", "config": {"syncMode": "IMPORT", "clientId": "your-azure-app-id", "clientSecret": "your-azure-client-secret", "tenant": "your-tenant-id-or-domain.com", "hostedDomain": "", "useJwksUrl": "true", "hideOnLoginPage": "false", "loginHint": "false", "uiLocales": "false", "backchannelSupported": "false", "disableUserInfo": "false", "acceptsPromptNoneForwardFromClient": "false", "filteredByClaim": "false"}}], "// === AUTHENTICATION FLOWS ===": "", "authenticationFlows": [{"alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "authenticatorFlow": false}, {"flowAlias": "forms", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "authenticatorFlow": true}]}], "// === BROWSER SECURITY HEADERS ===": "", "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=********; includeSubDomains"}, "// === SAMPLE USERS (Remove in production) ===": "", "users": [{"username": "admin", "enabled": true, "emailVerified": true, "firstName": "Admin", "lastName": "User", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "admin123", "temporary": true}], "realmRoles": ["admin", "user"], "clientRoles": {"account": ["view-profile", "manage-account"]}}, {"username": "testuser", "enabled": true, "emailVerified": true, "firstName": "Test", "lastName": "User", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "test123", "temporary": true}], "realmRoles": ["user"], "clientRoles": {"account": ["view-profile", "manage-account"]}}], "// === CLIENT SCOPES ===": "", "clientScopes": [{"name": "openwebui-scope", "description": "OpenWebUI-specific claims and permissions", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false", "consent.screen.text": "OpenWebUI access permissions"}, "protocolMappers": [{"name": "openwebui-role-mapper", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "openwebui_roles", "jsonType.label": "String", "multivalued": "true"}}, {"name": "openwebui-permissions", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "openwebui_permissions", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "permissions", "jsonType.label": "String"}}, {"name": "openwebui-user-id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "user_id", "jsonType.label": "String"}}]}]}