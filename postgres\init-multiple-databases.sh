#!/bin/bash
set -e

# Script to initialize multiple databases in PostgreSQL
# This script creates separate databases for Keycloak and OpenWebUI

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Create OpenWebUI database and user
    CREATE DATABASE openwebui;
    CREATE USER openwebui WITH ENCRYPTED PASSWORD '${OPENWEBUI_DB_PASSWORD:-changeme}';
    GRANT ALL PRIVILEGES ON DATABASE openwebui TO openwebui;
    
    -- Grant additional permissions for OpenWebUI user
    \c openwebui;
    GRANT ALL ON SCHEMA public TO openwebui;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO openwebui;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO openwebui;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO openwebui;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO openwebui;
    
    -- Switch back to default database
    \c $POSTGRES_DB;
    
    -- Ensure Keycloak user has proper permissions
    GRANT ALL PRIVILEGES ON DATABASE keycloak TO keycloak;
EOSQL

echo "Multiple databases initialized successfully!"
